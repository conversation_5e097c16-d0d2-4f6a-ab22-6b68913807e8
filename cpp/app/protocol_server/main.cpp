#include <spdlog/spdlog.h>

#include <atomic>
#include <chrono>
#include <iostream>
#include <memory>
#include <thread>

#include "zexuan/base/logger_manager.hpp"
#include "zexuan/net/event_loop.hpp"
#include "zexuan/protocol/server/protocol_server.hpp"

using namespace zexuan::protocol::server;
using namespace zexuan::base;
using namespace zexuan::net;

// 全局变量，用于信号处理
std::shared_ptr<ProtocolServer> g_server = nullptr;
std::shared_ptr<EventLoop> g_event_loop = nullptr;
std::atomic<bool> g_running{true};

int main(int argc, char* argv[]) {
  // 初始化日志系统
  std::string config_path = "./config/config.json";
  if (argc > 1) {
    config_path = argv[1];
  }

  if (!LoggerManager::initialize("protocol_server.log", config_path)) {
    std::cerr << "Failed to initialize logger" << std::endl;
    return -1;
  }

  spdlog::info("Protocol Test Server starting...");

  try {
    // 创建事件循环
    g_event_loop = std::make_shared<EventLoop>();

    // 注册优雅关闭信号
    g_event_loop->registerShutdownSignals([&](int signal) {
      spdlog::info("Received signal {}, shutting down gracefully...", signal);
      g_running.store(false);
      if (g_server) {
        g_server->Stop();
      }
    });

    spdlog::info("Using config file: {}", config_path);
    g_server = std::make_shared<ProtocolServer>(g_event_loop.get(), config_path);

    // 初始化服务器
    if (!g_server->Initialize()) {
      spdlog::error("Failed to initialize protocol server");
      return -1;
    }

    spdlog::info("Protocol server initialized successfully");
    spdlog::info("Server is ready to start");
    spdlog::info("Press Ctrl+C to stop the server");

    // 启动服务器
    if (!g_server->Start()) {
      spdlog::error("Failed to start protocol server");
      return -1;
    }

    spdlog::info("Protocol server started, running event loop...");

    // 运行事件循环（这会阻塞直到quit()被调用）
    g_event_loop->loop();

    spdlog::info("Event loop stopped, protocol server has stopped");

    // 确保完全清理服务器，等待所有工作线程结束
    g_server->Shutdown();
    g_server.reset();

    // 清理事件循环
    g_event_loop.reset();

  } catch (const std::exception& e) {
    spdlog::error("Exception in main: {}", e.what());

    // 异常情况下也要清理服务器
    if (g_server) {
      g_server->Shutdown();
      g_server.reset();
    }

    // 清理事件循环
    if (g_event_loop) {
      g_event_loop.reset();
    }
    return -1;
  }

  return 0;
}