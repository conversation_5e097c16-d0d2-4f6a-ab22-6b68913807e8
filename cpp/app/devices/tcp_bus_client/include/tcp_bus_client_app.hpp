/**
 * @file tcp_bus_client_app.hpp
 * @brief TCP总线客户端应用程序主类定义
 * <AUTHOR>
 * @date 2025-08-25
 */

#ifndef TCP_BUS_CLIENT_APP_HPP
#define TCP_BUS_CLIENT_APP_HPP

#include "message_processor.hpp"
#include "type1_processor.hpp"
#include "type2_processor.hpp"
#include "event_reporter.hpp"
#include "zexuan/bus/tcp_bus_client.hpp"
#include "zexuan/net/event_loop.hpp"
#include "zexuan/base/message.hpp"
#include "zexuan/base/types/structs.hpp"
#include <memory>
#include <vector>
#include <atomic>
#include <spdlog/spdlog.h>

namespace tcp_bus_client {

/**
 * @brief TCP总线客户端应用程序主类
 * 
 * 统一管理所有消息处理器和事件上报器，提供完整的客户端功能
 */
class TcpBusClientApp {
public:
    /**
     * @brief 构造函数
     * @param event_loop 事件循环引用
     * @param server_host 服务器地址
     * @param server_port 服务器端口
     * @param client_name 客户端名称
     * @param client_id 客户端ID
     */
    TcpBusClientApp(zexuan::net::EventLoop& event_loop,
                    const std::string& server_host,
                    uint16_t server_port,
                    const std::string& client_name,
                    int client_id = 9)
        : event_loop_(event_loop),
          client_(std::make_unique<zexuan::bus::TcpBusClient>(&event_loop, server_host, server_port, client_name)),
          running_(false) {
        
        // 设置客户端ID
        client_->setClientId(client_id);
        
        // 设置回调函数
        client_->setCommonMessageCallback([this](const zexuan::base::CommonMessage& msg) {
            onCommonMessage(msg);
        });
        
        client_->setEventMessageCallback([this](const zexuan::base::EventMessage& msg) {
            onEventMessage(msg);
        });
        
        client_->setControlMessageCallback([this](const zexuan::base::ControlMessage& msg) {
            onControlMessage(msg);
        });
        
        // 初始化消息处理器
        initializeProcessors();
        
        // 初始化事件上报器
        event_reporter_ = std::make_unique<EventReporter>(*client_, event_loop_);
    }

    /**
     * @brief 析构函数
     */
    ~TcpBusClientApp() {
        stop();
    }

    /**
     * @brief 启动应用程序
     * @return true表示启动成功，false表示启动失败
     */
    bool start() {
        if (running_.load()) {
            spdlog::warn("TcpBusClientApp is already running");
            return false;
        }

        spdlog::info("Starting TCP Bus Client App...");
        
        // 启动连接
        client_->connect();
        running_.store(true);
        
        // 设置连接检查和订阅
        setupConnectionAndSubscription();
        
        return true;
    }

    /**
     * @brief 停止应用程序
     */
    void stop() {
        if (!running_.load()) {
            return;
        }

        spdlog::info("Stopping TCP Bus Client App...");
        running_.store(false);
        
        // 停止事件上报
        if (event_reporter_) {
            event_reporter_->stop();
        }
        
        // 断开连接
        if (client_) {
            client_->disconnect();
        }
        
        spdlog::info("TCP Bus Client App stopped");
    }

    /**
     * @brief 检查是否正在运行
     * @return true表示正在运行，false表示已停止
     */
    bool isRunning() const {
        return running_.load();
    }

    /**
     * @brief 获取客户端引用
     * @return TCP总线客户端引用
     */
    zexuan::bus::TcpBusClient& getClient() {
        return *client_;
    }

private:
    /**
     * @brief 初始化消息处理器
     */
    void initializeProcessors() {
        // 创建Type1处理器
        processors_.push_back(std::make_unique<Type1Processor>(*client_));
        
        // 创建Type2处理器
        processors_.push_back(std::make_unique<Type2Processor>(*client_, event_loop_));
        
        spdlog::info("Initialized {} message processors", processors_.size());
    }

    /**
     * @brief 设置连接检查和订阅
     */
    void setupConnectionAndSubscription() {
        // 使用成员函数来避免递归lambda的内存泄漏问题
        checkConnectionAndSubscribe();

        // 设置连接超时检查
        event_loop_.runAfter(10.0, [this]() {
            if (!client_->isConnected()) {
                spdlog::error("Failed to connect to server after 10 seconds");
                stop();
                event_loop_.quit();
            }
        });
    }

    /**
     * @brief 检查连接状态并订阅
     */
    void checkConnectionAndSubscribe() {
        if (!running_.load()) {
            return;
        }

        if (!client_->isConnected()) {
            // 每100ms检查一次连接状态
            event_loop_.runAfter(0.1, [this]() {
                checkConnectionAndSubscribe();
            });
            return;
        }

        spdlog::info("Connected to server successfully");

        // 订阅所有消息类型和事件类型
        std::vector<int> message_types;
        std::vector<int> event_types;

        // 订阅所有CommonMessage类型 (假设类型1-10)
        for (int i = 1; i <= 10; ++i) {
            message_types.push_back(i);
        }

        // 订阅统一的事件类型1
        event_types.push_back(1);

        bool subscribe_result = client_->subscribe(message_types, event_types);
        spdlog::info("Subscribe result: {}", subscribe_result);

        if (subscribe_result) {
            spdlog::info("Sent subscription request for all message types");

            // 启动事件上报
            if (event_reporter_) {
                event_reporter_->start();
                spdlog::info("Started automatic event reporting");
            }
        } else {
            spdlog::error("Failed to send subscription request");
        }
    }

    /**
     * @brief CommonMessage回调处理
     */
    void onCommonMessage(const zexuan::base::CommonMessage& msg) {
        spdlog::info("Received CommonMessage: type={}, source_id={}, target_id={}, invoke_id={}, data_size={}",
                    static_cast<int>(msg.type), msg.source_id, msg.target_id, msg.invoke_id, msg.data.size());

        handleReceivedMessage(msg);
    }

    /**
     * @brief EventMessage回调处理
     */
    void onEventMessage(const zexuan::base::EventMessage& msg) {
        spdlog::info("Received EventMessage: event_type={}, device_id={}, source_id={}, description={}, data_size={}",
                    msg.event_type, msg.device_uuid.device_id, msg.source_id, msg.description, msg.data.size());
        
        // EventMessage暂时不处理，只记录
        spdlog::debug("EventMessage received but not processed");
    }

    /**
     * @brief ControlMessage回调处理
     */
    void onControlMessage(const zexuan::base::ControlMessage& msg) {
        spdlog::info("Received ControlMessage: action={}, success={}, message={}", 
                    msg.action, msg.success, msg.message);
        
        if (msg.action == "subscribe_response") {
            if (msg.success) {
                spdlog::info("Subscription successful. Subscribed message types: {}, event types: {}", 
                            msg.subscribed_message_types.size(), msg.subscribed_event_types.size());
            } else {
                spdlog::error("Subscription failed: {}", msg.message);
            }
        }
    }

    /**
     * @brief 处理接收到的消息
     */
    void handleReceivedMessage(const zexuan::base::CommonMessage& message) {
        spdlog::info("Processing CommonMessage: type={}, source_id={}, invoke_id={}",
                    static_cast<int>(message.type), message.source_id, message.invoke_id);

        // 只处理来自ProtocolService的消息（避免处理自己发送的消息）
        if (message.source_id == zexuan::base::SERVICE_SUBJECT_ID) {  // SERVICE_SUBJECT_ID = 2050
            processBusinessMessage(message);
        } else {
            spdlog::debug("Ignoring message not from ProtocolService (source_id={}, expected={})", 
                         message.source_id, zexuan::base::SERVICE_SUBJECT_ID);
        }
    }

    /**
     * @brief 处理业务消息
     */
    void processBusinessMessage(const zexuan::base::CommonMessage& original_message) {
        spdlog::debug("Processing business message: invoke_id={}", original_message.invoke_id);

        // 尝试解析输入的IEC103消息
        zexuan::base::Message input_msg;
        size_t parsed = input_msg.deserialize(original_message.data);

        if (parsed > 0) {
            // 成功解析为IEC103消息
            spdlog::debug("Parsed input IEC103 message: TYP={:02X}, COT={:02X}, FUN={:02X}, INF={:02X}",
                         input_msg.getTyp(), input_msg.getVsq(), input_msg.getCot(), input_msg.getFun(),
                         input_msg.getInf());

            // 查找合适的处理器
            bool processed = false;
            for (auto& processor : processors_) {
                if (processor->canProcess(input_msg.getTyp())) {
                    spdlog::debug("Using processor: {}", processor->getName());
                    processed = processor->processMessage(original_message, input_msg);
                    break;
                }
            }

            if (!processed) {
                // 没有找到合适的处理器，发送简单响应
                spdlog::debug("No suitable processor found, sending simple response");
                sendSimpleResponse(original_message);
            }
        } else {
            // 不能解析为IEC103，生成简单响应
            sendSimpleResponse(original_message);
        }
    }

    /**
     * @brief 发送简单响应
     */
    void sendSimpleResponse(const zexuan::base::CommonMessage& original_message) {
        spdlog::debug("Sending simple response for non-IEC103 message");

        zexuan::base::CommonMessage response;
        response.type = zexuan::base::MessageType::RESULT;
        response.source_id = client_->getClientId();
        response.target_id = original_message.source_id;
        response.invoke_id = original_message.invoke_id;
        response.b_lastmsg = true;

        // 生成简单响应
        std::string simple_response = "SIMPLE_RESPONSE_FOR_" + original_message.invoke_id;
        response.data.assign(simple_response.begin(), simple_response.end());

        // 发送响应到总线
        if (client_->sendCommonMessage(response)) {
            spdlog::debug("Sent simple response to bus: invoke_id={}", response.invoke_id);
        } else {
            spdlog::error("Failed to send simple response to bus: invoke_id={}", response.invoke_id);
        }
    }

    zexuan::net::EventLoop& event_loop_;                                    ///< 事件循环引用
    std::unique_ptr<zexuan::bus::TcpBusClient> client_;                    ///< TCP总线客户端
    std::vector<std::unique_ptr<MessageProcessor>> processors_;            ///< 消息处理器列表
    std::unique_ptr<EventReporter> event_reporter_;                        ///< 事件上报器
    std::atomic<bool> running_;                                            ///< 运行状态标志
};

} // namespace tcp_bus_client

#endif // TCP_BUS_CLIENT_APP_HPP
