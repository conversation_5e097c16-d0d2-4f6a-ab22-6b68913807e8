/**
 * @file event_reporter.hpp
 * @brief 事件上报器定义
 * <AUTHOR>
 * @date 2025-08-25
 */

#ifndef TCP_BUS_CLIENT_EVENT_REPORTER_HPP
#define TCP_BUS_CLIENT_EVENT_REPORTER_HPP

#include "zexuan/bus/tcp_bus_client.hpp"
#include "zexuan/net/event_loop.hpp"
#include "zexuan/base/types/structs.hpp"
#include <atomic>
#include <chrono>
#include <spdlog/spdlog.h>

namespace tcp_bus_client {

/**
 * @brief 事件上报器
 * 
 * 负责定时生成和发送时间戳事件到总线
 */
class EventReporter {
public:
    /**
     * @brief 构造函数
     * @param client TCP总线客户端引用
     * @param event_loop 事件循环引用
     * @param interval_seconds 上报间隔（秒）
     */
    EventReporter(zexuan::bus::TcpBusClient& client, 
                  zexuan::net::EventLoop& event_loop,
                  double interval_seconds = 3.0)
        : client_(client), event_loop_(event_loop), interval_seconds_(interval_seconds), running_(false) {}

    /**
     * @brief 析构函数
     */
    ~EventReporter() {
        stop();
    }

    /**
     * @brief 启动事件上报
     */
    void start() {
        if (running_.load()) {
            spdlog::warn("Event reporter is already running");
            return;
        }

        running_.store(true);
        spdlog::info("Event reporter started with interval {} seconds", interval_seconds_);

        // 使用定时器定期生成时间戳事件
        event_loop_.runEvery(interval_seconds_, [this]() {
            if (running_.load()) {
                generateTimestampEvent();
            }
        });
    }

    /**
     * @brief 停止事件上报
     */
    void stop() {
        if (!running_.load()) {
            return;
        }

        running_.store(false);
        spdlog::info("Event reporter stopped");
    }

    /**
     * @brief 检查是否正在运行
     * @return true表示正在运行，false表示已停止
     */
    bool isRunning() const {
        return running_.load();
    }

    /**
     * @brief 设置上报间隔
     * @param interval_seconds 新的上报间隔（秒）
     */
    void setInterval(double interval_seconds) {
        interval_seconds_ = interval_seconds;
        spdlog::info("Event reporter interval updated to {} seconds", interval_seconds_);
    }

    /**
     * @brief 获取当前上报间隔
     * @return 当前上报间隔（秒）
     */
    double getInterval() const {
        return interval_seconds_;
    }

private:
    /**
     * @brief 生成并发送时间戳事件
     */
    void generateTimestampEvent() {
        if (!client_.isConnected()) {
            spdlog::debug("Client not connected, skipping timestamp event generation");
            return;
        }

        // 创建时间戳事件
        zexuan::base::EventMessage event_msg;
        event_msg.event_type = 1;  // 统一的事件类型
        event_msg.source_id = client_.getClientId();  // 使用客户端ID
        event_msg.description = "Automatic timestamp event from tcp_bus_client";

        // 添加当前时间戳作为数据
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        std::string timestamp = std::to_string(time_t);
        event_msg.data.assign(timestamp.begin(), timestamp.end());

        // 发送事件到总线
        if (client_.sendEventMessage(event_msg)) {
            spdlog::debug("Generated and sent timestamp event: {}", timestamp);
        } else {
            spdlog::error("Failed to send timestamp event");
        }
    }

    zexuan::bus::TcpBusClient& client_;     ///< TCP总线客户端引用
    zexuan::net::EventLoop& event_loop_;    ///< 事件循环引用
    double interval_seconds_;               ///< 上报间隔（秒）
    std::atomic<bool> running_;             ///< 运行状态标志
};

} // namespace tcp_bus_client

#endif // TCP_BUS_CLIENT_EVENT_REPORTER_HPP
