# TCP消息总线客户端CMakeLists.txt

# 设置源文件
set(SOURCES
    source/main.cpp
)

# 设置头文件
set(HEADERS
    include/message_processor.hpp
    include/type1_processor.hpp
    include/type2_processor.hpp
    include/event_reporter.hpp
    include/tcp_bus_client_app.hpp
)

# 创建可执行文件
add_executable(tcp_bus_client ${SOURCES} ${HEADERS})

# 链接库
target_link_libraries(tcp_bus_client
    PRIVATE
        zexuan_base
        zexuan_net
        zexuan_bus
        nlohmann_json::nlohmann_json
        spdlog::spdlog
)

# 设置C++标准
target_compile_features(tcp_bus_client PRIVATE cxx_std_17)

# 包含头文件目录
target_include_directories(tcp_bus_client
    PRIVATE
        ${CMAKE_SOURCE_DIR}/cpp/include
        ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# 设置输出目录
set_target_properties(tcp_bus_client PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin/devices/
)

# 安装可执行文件
install(TARGETS tcp_bus_client
    RUNTIME DESTINATION bin
    COMPONENT applications
)
