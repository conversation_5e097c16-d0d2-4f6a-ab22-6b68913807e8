# Signal 集成设计书

## 项目概述与目标

### 项目名称
EventLoop 信号处理系统集成

### 项目目标
将信号处理集成到现有的 EventLoop 事件循环系统中，实现基于 `signalfd` 的异步信号处理，替代传统的信号处理方式，提供更好的可控性和线程安全性。

### 核心价值
- **统一事件模型**：信号事件与 I/O 事件在同一个事件循环中处理
- **线程安全**：避免传统信号处理的竞态条件和异步安全问题
- **优雅关闭**：实现系统的优雅关闭流程
- **可扩展性**：为后续功能扩展提供信号通知机制

## 需求分析

### 功能性需求

#### 1. 基础信号处理
- 支持常用系统信号：SIGINT, SIGTERM
- 信号回调函数注册和注销机制
- 多信号并发处理能力

#### 2. 优雅关闭机制
- 接收到 SIGINT/SIGTERM 后执行回调

## 技术架构设计

### 1. 核心组件架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   EventLoop     │    │  SignalHandler  │    │     Channel     │
│                 │    │                 │    │                 │
│ - registerSignal│◄──►│ - signalfd_     │◄──►│ - handleRead    │
│ - unregisterSig │    │ - signalMask_   │    │ - setCallback   │
│ - signalHandler_│    │ - callbacks_    │    │ - enableReading │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Poller      │    │   signalfd()    │    │   epoll_wait    │
│                 │    │                 │    │                 │
│ - poll()        │    │ - SFD_NONBLOCK  │    │ - 监听 signalfd │
│ - updateChannel │    │ - SFD_CLOEXEC   │    │ - 触发回调      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2. 类设计详情

#### SignalHandler 类
```cpp
class SignalHandler {
public:
    using SignalCallback = std::function<void(int signal)>;

    explicit SignalHandler(EventLoop* loop);
    ~SignalHandler();

    // 信号注册接口
    void registerSignal(int signal, SignalCallback cb);
    void unregisterSignal(int signal);

    // 便捷接口
    void registerShutdownSignals(SignalCallback cb);
    void registerUserSignals(SignalCallback cb);

private:
    void handleRead();
    void updateSignalMask();

    EventLoop* loop_;
    int signalfd_;
    std::unique_ptr<Channel> signalChannel_;
    std::map<int, SignalCallback> signalCallbacks_;
    sigset_t signalMask_;
};
```

#### EventLoop 扩展
```cpp
class EventLoop {
    // ... 现有接口 ...

public:
    // 信号处理接口
    void registerSignal(int signal, std::function<void(int)> cb);
    void unregisterSignal(int signal);

private:
    std::unique_ptr<SignalHandler> signalHandler_;
};
```

### 3. 信号处理流程

```
用户程序发送信号 (kill -TERM pid)
         │
         ▼
系统内核接收信号
         │
         ▼
signalfd 变成可读状态
         │
         ▼
epoll_wait 返回 signalfd 事件
         │
         ▼
Channel::handleEvent 被调用
         │
         ▼
SignalHandler::handleRead 执行
         │
         ▼
读取 signalfd_siginfo 结构
         │
         ▼
查找对应的回调函数
         │
         ▼
执行用户注册的回调函数
```

## 详细开发计划

### 阶段一：SignalHandler 基础实现 (1-2 天)

#### [ ] 任务1.1：创建 SignalHandler 类框架
**描述**：创建 SignalHandler 类的基本结构和接口定义
**实现文件**：
- `cpp/include/zexuan/net/signal_handler.hpp`
- `cpp/source/zexuan/net/signal_handler.cpp`

**核心实现**：
```cpp
// 创建 signalfd
int createSignalfd(const sigset_t* mask) {
    int sfd = ::signalfd(-1, mask, SFD_NONBLOCK | SFD_CLOEXEC);
    if (sfd < 0) {
        spdlog::critical("Failed to create signalfd: {}", strerror(errno));
        abort();
    }
    return sfd;
}

// 阻塞信号的默认处理
void blockSignals(const sigset_t* mask) {
    if (::pthread_sigmask(SIG_BLOCK, mask, nullptr) != 0) {
        spdlog::critical("Failed to block signals: {}", strerror(errno));
        abort();
    }
}
```

**验收标准**：
- 类可以正常编译和链接
- signalfd 可以正确创建
- 信号掩码设置正确
- 错误处理完善

#### [ ] 任务1.2：信号注册和注销机制
**描述**：实现信号回调函数的注册和注销功能
**核心实现**：
```cpp
void SignalHandler::registerSignal(int signal, SignalCallback cb) {
    loop_->assertInLoopThread();

    signalCallbacks_[signal] = std::move(cb);

    // 更新信号掩码
    sigaddset(&signalMask_, signal);
    updateSignalMask();
}

void SignalHandler::updateSignalMask() {
    // 阻塞信号的默认处理
    blockSignals(&signalMask_);

    // 更新 signalfd
    if (::signalfd(signalfd_, &signalMask_, SFD_NONBLOCK | SFD_CLOEXEC) < 0) {
        spdlog::error("Failed to update signalfd: {}", strerror(errno));
    }
}
```

**验收标准**：
- 可以注册多个不同信号的回调
- 可以注销已注册的信号
- 信号掩码正确更新
- 线程安全保证

#### [ ] 任务1.3：信号事件读取和分发
**描述**：实现从 signalfd 读取信号信息并分发到对应回调
**核心实现**：
```cpp
void SignalHandler::handleRead() {
    struct signalfd_siginfo si;

    while (true) {
        ssize_t s = ::read(signalfd_, &si, sizeof(si));

        if (s == sizeof(si)) {
            int signal = si.ssi_signo;
            spdlog::debug("Received signal: {}", signal);

            auto it = signalCallbacks_.find(signal);
            if (it != signalCallbacks_.end()) {
                it->second(signal);  // 执行回调
            } else {
                spdlog::warn("No handler for signal: {}", signal);
            }
        } else if (s < 0) {
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                break;  // 没有更多信号
            } else {
                spdlog::error("Failed to read signalfd: {}", strerror(errno));
                break;
            }
        } else {
            spdlog::error("Partial read from signalfd: {} bytes", s);
            break;
        }
    }
}
```

**验收标准**：
- 能正确读取信号信息
- 回调函数正确执行
- 处理读取错误情况
- 支持批量信号处理

### 阶段二：EventLoop 集成 (1 天)

#### [ ] 任务2.1：EventLoop 接口扩展
**描述**：在 EventLoop 中集成 SignalHandler
**实现要点**：
- 在 EventLoop 构造函数中创建 SignalHandler
- 添加信号处理的公共接口
- 确保线程安全

**核心实现**：
```cpp
// 在 EventLoop 构造函数中
EventLoop::EventLoop()
    : // ... 其他初始化 ...
      signalHandler_(std::make_unique<SignalHandler>(this)) {
    // ... 其他初始化代码 ...
}

void EventLoop::registerSignal(int signal, std::function<void(int)> cb) {
    signalHandler_->registerSignal(signal, std::move(cb));
}

void EventLoop::unregisterSignal(int signal) {
    signalHandler_->unregisterSignal(signal);
}
```

**验收标准**：
- EventLoop 可以注册信号处理器
- 信号处理在正确的线程中执行
- 不影响现有的 I/O 事件处理
- 内存管理正确

#### [ ] 任务2.2：便捷接口实现
**描述**：提供常用信号的便捷注册接口
**实现要点**：
```cpp
void SignalHandler::registerShutdownSignals(SignalCallback cb) {
    registerSignal(SIGINT, cb);
    registerSignal(SIGTERM, cb);
}

void SignalHandler::registerUserSignals(SignalCallback cb) {
    registerSignal(SIGUSR1, cb);
    registerSignal(SIGUSR2, cb);
}
```

**验收标准**：
- 提供批量注册接口
- 支持相同回调处理多个信号
- API 使用简单直观

### 阶段三：应用集成 (1-2 天)

#### [ ] 任务3.1：TCP Bus Server 集成
**描述**：将信号处理集成到 tcp_bus_server 应用中
**实现要点**：
- 移除现有的 `signal()` 函数调用
- 使用 EventLoop 的信号处理接口
- 实现优雅关闭逻辑

**核心实现**：
```cpp
int main() {
    EventLoop loop;

    // 注册优雅关闭信号
    loop.registerSignal(SIGINT, [&](int signal) {
        spdlog::info("Received SIGINT, shutting down gracefully...");
        g_running.store(false);
        loop.quit();
    });

    loop.registerSignal(SIGTERM, [&](int signal) {
        spdlog::info("Received SIGTERM, shutting down gracefully...");
        g_running.store(false);
        loop.quit();
    });

    // 注册配置重载信号
    loop.registerSignal(SIGUSR1, [&](int signal) {
        spdlog::info("Received SIGUSR1, reloading configuration...");
        // TODO: 实现配置重载逻辑
    });

    loop.loop();
    return 0;
}
```

**验收标准**：
- Ctrl+C 可以优雅关闭服务器
- 关闭过程中正确清理资源
- 现有连接得到妥善处理
- 日志记录完整

#### [ ] 任务3.2：TCP Bus Client 集成
**描述**：将信号处理集成到 tcp_bus_client 应用中
**实现要点**：
- 统一信号处理方式
- 确保客户端优雅断开连接
- 处理重连过程中的信号

**验收标准**：
- 客户端可以优雅关闭
- 断开连接时通知服务器
- 资源正确释放
- 重连定时器正确取消

#### [ ] 任务3.3：向后兼容性测试
**描述**：确保信号处理不影响现有功能
**测试内容**：
- 网络连接功能正常
- 定时器功能正常
- 性能没有明显下降
- 内存使用稳定

**验收标准**：
- 所有现有功能测试通过
- 性能测试满足要求
- 内存泄漏检测通过

### 阶段四：测试和优化 (1 天)

#### [ ] 任务4.1：单元测试编写
**描述**：编写 SignalHandler 的完整单元测试
**测试用例**：
- 信号注册和注销功能
- 信号触发和回调执行
- 多信号并发处理
- 错误情况处理
- 边界条件测试

**测试框架**：使用现有的测试框架
**覆盖率目标**：> 85%

**验收标准**：
- 所有测试用例通过
- 代码覆盖率达标
- 包含性能测试
- 包含稳定性测试

## 关键技术要点

### 1. signalfd 使用要点
```cpp
// 创建 signalfd 前必须先阻塞信号
sigset_t mask;
sigemptyset(&mask);
sigaddset(&mask, SIGINT);
sigaddset(&mask, SIGTERM);

// 阻塞信号的默认处理
pthread_sigmask(SIG_BLOCK, &mask, nullptr);

// 创建 signalfd
int sfd = signalfd(-1, &mask, SFD_NONBLOCK | SFD_CLOEXEC);
```

### 2. 线程安全考虑
- 信号掩码在进程级别生效
- signalfd 只在创建它的线程中有效
- 回调函数在 EventLoop 线程中执行
- 避免在信号处理中调用非异步安全函数

### 3. 错误处理策略
- signalfd 创建失败时程序终止
- 信号读取失败时记录日志并继续
- 回调函数异常时捕获并记录
- 提供降级处理机制

## 预期成果

完成后的信号处理系统将提供：

1. **统一的事件处理模型**：信号和 I/O 事件在同一循环中处理
2. **线程安全的信号处理**：避免传统信号处理的竞态条件
3. **优雅的系统关闭**：接收信号后优雅清理资源
4. **灵活的信号回调机制**：支持自定义信号处理逻辑
5. **完善的错误处理**：异常情况下的稳定运行
6. **高性能表现**：信号处理不影响正常业务

这将显著提升 TCP Bus 系统的健壮性、可维护性和用户体验。